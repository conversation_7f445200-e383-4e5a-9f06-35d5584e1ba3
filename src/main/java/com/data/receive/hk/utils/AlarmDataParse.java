package com.data.receive.hk.utils;

import com.data.receive.hk.domain.bo.HCNetSDK;
import com.data.receive.hk.service.AlarmService;
import com.data.receive.hk.service.EquService;
import com.data.receive.hk.service.impl.AlarmServiceImpl;
import com.data.receive.hk.service.impl.EquServiceImpl;
import com.sun.jna.Pointer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @create 2022-08-15-18:04
 */
@Component
public class AlarmDataParse {

    private static final Logger log = LoggerFactory.getLogger(AlarmDataParse.class);
    
    private static AlarmService alarmService;
    
    @Autowired
    private AlarmService alarmServiceTemp;
    
    @PostConstruct
    public void init() {
        alarmService = alarmServiceTemp;
    }

    public static void alarmDataHandle(int lCommand, HCNetSDK.NET_DVR_ALARMER pAlarmer, Pointer pAlarmInfo, int dwBufLen, Pointer pUser) {

        log.info("登录用户ID：{}",pAlarmer.lUserID);

        String deviceIp = bytesToString(pAlarmer.sDeviceIP);
        log.info("设备IP：{}", deviceIp);

        log.info("接收海康报警数据，报警事件类型：{}，开始",Integer.toHexString(lCommand));
        //lCommand是传的报警类型
        switch (lCommand) {
            case HCNetSDK.COMM_ALARM_V30:  //移动侦测、视频丢失、遮挡、IO信号量等报警信息(V3.0以上版本支持的设备)
                HCNetSDK.NET_DVR_ALARMINFO_V30 struAlarmInfo = new HCNetSDK.NET_DVR_ALARMINFO_V30();
                struAlarmInfo.write();
                Pointer pAlarmInfo_V30 = struAlarmInfo.getPointer();
                pAlarmInfo_V30.write(0, pAlarmInfo.getByteArray(0, struAlarmInfo.size()), 0, struAlarmInfo.size());
                struAlarmInfo.read();
                alarmService.addAlarm(deviceIp, struAlarmInfo.dwAlarmType);
                break;
            default:
                log.warn("不处理，报警类型：" + Integer.toHexString(lCommand));
                break;
        }
        log.info("接收海康报警数据，报警事件类型：{}，结束"+ Integer.toHexString(lCommand));
    }

    public static String bytesToString(byte[] bytes) {
        int len = 0;
        while (len < bytes.length && bytes[len] != 0) {
            len++;
        }
        return new String(bytes, 0, len, StandardCharsets.UTF_8);
    }
}
