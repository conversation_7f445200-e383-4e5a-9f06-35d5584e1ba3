package com.data.receive.hk.listener.handle;

import com.data.receive.hk.domain.bo.HCNetSDK;
import com.data.receive.hk.utils.AlarmDataParse;
import com.sun.jna.Pointer;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @create 2022-08-15-17:26
 */
@Slf4j
public class FMSGCallBack implements HCNetSDK.FMSGCallBack {
    //报警信息回调函数
    @Override
    public void invoke(int lCommand, HCNetSDK.NET_DVR_ALARMER pAlarmer, Pointer pAlarmInfo, int dwBufLen, Pointer pUser) {
        log.info("接收到报警数据");
        AlarmDataParse.alarmDataHandle(lCommand, pAlarmer, pAlarmInfo, dwBufLen, pUser);
    }
}
