package com.data.receive.hk.listener;

import com.data.receive.hk.domain.bo.HCNetSDK;
import com.data.receive.hk.domain.vo.EquInfoVO;
import com.data.receive.hk.listener.handle.FMSGCallBack;
import com.data.receive.hk.listener.handle.FMSGCallBack_V31;
import com.data.receive.hk.service.EquService;
import com.sun.jna.Native;
import com.sun.jna.Pointer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2025/3/13
 */
@Component
public class HaiKangAlarmListener implements CommandLineRunner {

    private static final Logger log = LoggerFactory.getLogger(HaiKangAlarmListener.class);

    static HCNetSDK hCNetSDK = null;
    /**
     * 报警监听句柄
     */
    static int lListenHandle = -1;
    /**
     * 报警监听回调函数
     */
    static FMSGCallBack fMSFCallBack=null;
    /**
    *报警布防回调函数
     */
    static FMSGCallBack_V31 fMSFCallBack_V31 = null;

    @Resource
    private EquService equService;


    private final ConcurrentHashMap<Integer,Integer> lUserIdMap = new ConcurrentHashMap<>();

    @Override
    public void run(String... strings) {
        publishSubscribe();
    }

    @PreDestroy
    public void destory(){
        lUserIdMap.forEach((key,value)->closedAlarmChan(value));
    }

    /**
     * 动态库加载
     *
     * @return
     */
    private boolean createSDKInstance() {
        if (hCNetSDK == null) {
            synchronized (HCNetSDK.class) {
                try {
                    String strDllPath = System.getProperty("user.dir") + "\\lib\\HCNetSDK.dll";
                    hCNetSDK = (HCNetSDK) Native.loadLibrary(strDllPath, HCNetSDK.class);
                } catch (Exception ex) {
                    log.error("海康报警监听加载SDK文件失败：",hCNetSDK.NET_DVR_GetLastError());
                    return false;
                }
            }
        }
        /**初始化*/
        hCNetSDK.NET_DVR_Init();
        /**加载日志*/
        hCNetSDK.NET_DVR_SetLogToFile(3, "./sdklog1", false);
        return true;
    }

    /**
     * 开启监听
     *
     * @param ip   监听IP
     * @param port 监听端口
     */
    public int startListen(String ip, short port) {
        log.info("开启海康报警监听-开始");
        if (lListenHandle <= 0)
        {
            if (fMSFCallBack == null) {
                fMSFCallBack = new FMSGCallBack();
            }
            lListenHandle = hCNetSDK.NET_DVR_StartListen_V30(ip, port,fMSFCallBack, null);
            if (lListenHandle == -1) {
                System.out.println("监听失败" + hCNetSDK.NET_DVR_GetLastError());
                return -1;
            } else {
                log.info("开启海康报警监听-结束");
                return lListenHandle;
            }
        }else {
            log.info("监听已经开启，请先停止监听！");
            return lListenHandle;
        }
    }

    /**
     * 停止监听
     * @param Handle 监听句柄
     */
    public void stopListen(int Handle) {
        log.info("关闭海康报警监听-开始");
        if (Handle <= -1)
        {
            log.info("监听未开启");
            return;
        }
        if (!hCNetSDK.NET_DVR_StopListen_V30(Handle)) {
            log.error("停止监听失败：{}",hCNetSDK.NET_DVR_GetLastError());
            return;
        }
        log.info("关闭海康报警监听-结束");
    }

    /**
     * 发布订阅
     */
    public void publishSubscribe(){
        List<EquInfoVO> equList = equService.getEquList();
        if (CollectionUtils.isEmpty(equList)){
            log.info("未查询到海康设备");
            return;
        }
        log.info( "发布订阅海康摄像机报警-开始");
        log.info("海康报警监听加载SDK文件-开始");
        createSDKInstance();
        log.info("海康报警监听加载SDK文件-结束");
        log.info("设置报警回调函数-开始");
        createCallBack();
        log.info("设置报警回调函数-结束");
        equList.forEach(item->{
            log.info("设备:{}登录-开始",item.getEquName());
            int lUserId = loginDevice(item.getEquIp(), Short.parseShort(item.getEquPort()), item.getUserName(), item.getUserPassword());
            log.info("设备:{}登录-结束",item.getEquName());
            log.info("设备:{}布防-开始",item.getEquName());
            setAlarmChan(lUserId);
            log.info("设备:{}布防-结束",item.getEquName());
        });
        log.info( "发布订阅海康摄像机报警-结束");

    }

    /**
     * 报警布防
     * @param userID 设备登录句柄ID
     * @return
     */
    public void setAlarmChan(int userID) {
        if (userID == -1) {
            log.info("请先注册");
            return;
        }
        if (lUserIdMap.containsKey(userID)){
            log.info("该设备已布防，请先撤防");
            return;
        }
        //报警布防参数设置
        HCNetSDK.NET_DVR_SETUPALARM_PARAM alarmInfo  = new HCNetSDK.NET_DVR_SETUPALARM_PARAM();
        alarmInfo.dwSize = alarmInfo.size();
        //布防等级
        alarmInfo.byLevel = 0;
        // 智能交通报警信息上传类型：0- 老报警信息（NET_DVR_PLATE_RESULT），1- 新报警信息(NET_ITS_PLATE_RESULT)
        alarmInfo.byAlarmInfoType = 1;
        //布防类型：0-客户端布防，1-实时布防，客户端布防仅支持一路
        alarmInfo.byDeployType = 0;
        alarmInfo.write();
        int lAlarmHandle= hCNetSDK.NET_DVR_SetupAlarmChan_V41(userID, alarmInfo);
        if (lAlarmHandle == -1) {
            log.warn("布防失败，错误码为：{}",hCNetSDK.NET_DVR_GetLastError());
        } else {
            lUserIdMap.put(userID,lAlarmHandle);
        }
    }

    /**
     * 设备撤防
     * @param AlarmHandle 布防句柄
     */
    public void closedAlarmChan(int AlarmHandle) {
        if (AlarmHandle <= -1) {
            log.info("设备未布防，请先布防！");
            return ;
        }
        if (!hCNetSDK.NET_DVR_CloseAlarmChan(AlarmHandle)) {
            System.err.println("撤防失败，err "+hCNetSDK.NET_DVR_GetLastError());
            log.warn("撤防失败，"+hCNetSDK.NET_DVR_GetLastError());
            return;
        }
        log.info("撤防成功");
    }

    /**
     * 创建布防回调函数
     */
    public void createCallBack(){
        //设置报警回调函数
        if (fMSFCallBack_V31 == null) {
            fMSFCallBack_V31 = new FMSGCallBack_V31();
            Pointer pUser = null;
            if (!hCNetSDK.NET_DVR_SetDVRMessageCallBack_V31(fMSFCallBack_V31, pUser)) {
                log.warn("设置回调函数失败");
            }
        }
    }

    /**
     * 登录设备，支持 V40 和 V30 版本，功能一致。
     *
     * @param ip      设备IP地址
     * @param port    SDK端口，默认为设备的8000端口
     * @param user    设备用户名
     * @param psw     设备密码
     * @return 登录成功返回用户ID，失败返回-1
     */
    public int loginDevice(String ip, short port, String user, String psw) {
        // 创建设备登录信息和设备信息对象
        HCNetSDK.NET_DVR_USER_LOGIN_INFO loginInfo = new HCNetSDK.NET_DVR_USER_LOGIN_INFO();
        HCNetSDK.NET_DVR_DEVICEINFO_V40 deviceInfo = new HCNetSDK.NET_DVR_DEVICEINFO_V40();

        // 设置设备IP地址
        byte[] deviceAddress = new byte[HCNetSDK.NET_DVR_DEV_ADDRESS_MAX_LEN];
        byte[] ipBytes = ip.getBytes();
        System.arraycopy(ipBytes, 0, deviceAddress, 0, Math.min(ipBytes.length, deviceAddress.length));
        loginInfo.sDeviceAddress = deviceAddress;

        // 设置用户名和密码
        byte[] userName = new byte[HCNetSDK.NET_DVR_LOGIN_USERNAME_MAX_LEN];
        byte[] password = psw.getBytes();
        System.arraycopy(user.getBytes(), 0, userName, 0, Math.min(user.length(), userName.length));
        System.arraycopy(password, 0, loginInfo.sPassword, 0, Math.min(password.length, loginInfo.sPassword.length));
        loginInfo.sUserName = userName;

        // 设置端口和登录模式
        loginInfo.wPort = port;
        // 同步登录
        loginInfo.bUseAsynLogin = false;
        // 使用SDK私有协议
        loginInfo.byLoginMode = 0;

        // 执行登录操作
        int userID = hCNetSDK.NET_DVR_Login_V40(loginInfo, deviceInfo);
        if (userID == -1) {
            log.warn("登录失败，错误码为:{}",hCNetSDK.NET_DVR_GetLastError());
        } else {
            // 处理通道号逻辑
            int startDChan = deviceInfo.struDeviceV30.byStartDChan;
            log.info("预览起始通道号:{}",startDChan);
        }
        // 返回登录结果
        return userID;
    }
}
