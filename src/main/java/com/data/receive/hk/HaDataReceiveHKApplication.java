package com.data.receive.hk;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.PropertySource;

@MapperScan("com.data.receive.hk.dao")
@PropertySource(value = {"classpath:config/config.properties"}, encoding = "UTF-8")
@SpringBootApplication
public class HaDataReceiveHKApplication {

    public static void main(String[] args) {
        SpringApplication.run(HaDataReceiveHKApplication.class, args);
    }

}
