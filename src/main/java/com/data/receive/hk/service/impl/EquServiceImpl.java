package com.data.receive.hk.service.impl;

import com.data.receive.hk.domain.vo.AlarmInfoV3;
import com.data.receive.hk.domain.vo.EquInfoVO;
import com.data.receive.hk.service.EquService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/17
 */
@Service
public class EquServiceImpl implements EquService {
    private static final Logger log = LoggerFactory.getLogger(EquServiceImpl.class);
    @Value("#{'${equ.list}'.split(',')}")
    private List<String> equList;

    @Value("${alarm.class.name}")
    private String alarmClassName;
    @Value("${alarm.class.code}")
    private String alarmClassCode;
    @Value("${alarm.name.code}")
    private String alarmNameCode;
    @Value("${alarm.name}")
    private String alarmName;
    @Value("${alarm.level.code}")
    private String alarmLevelCode;
    @Value("${alarm.level.name}")
    private String alarmLevelName;
    @Value("${alarm.state.code}")
    private String alarmStateCode;
    @Value("${alarm.state.name}")
    private String alarmStateName;
    @Value("${system.code}")
    private String systemCode;
    @Value("${system.name}")
    private String systemName;
    @Value("${airport.iata}")
    private String airportIata;
    @Value("${airport.name}")
    private String airportName;


    @Override
    public List<EquInfoVO> getEquList() {
        List<EquInfoVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(equList)){
            for (String equ : equList) {
                String[] arr = equ.split(";");
                EquInfoVO vo = new EquInfoVO();
                vo.setEquId(arr[0]);
                vo.setEquName(arr[1]);
                vo.setEquCode(arr[2]);
                vo.setEquClassCode(arr[3]);
                vo.setEquClassName(arr[4]);
                vo.setEquIp(arr[5]);
                vo.setEquPort(arr[6]);
                vo.setUserName(arr[7]);
                vo.setUserPassword(arr[8]);
                vo.setGisX(arr[9]);
                vo.setGisY(arr[10]);
                list.add(vo);
            }
        }
        return list;
    }

    @Override
    public AlarmInfoV3 getAlarmInfo(String deviceIp) {
        EquInfoVO equInfoVO = getEquInfo(deviceIp);
        if (Objects.isNull(equInfoVO)){
            log.error("根据设备IP:{}未查询到设备信息",deviceIp);
            return null;
        }
        AlarmInfoV3 alarmInfoV3 = new AlarmInfoV3();
        alarmInfoV3.setAlarmSourceId(UUID.randomUUID().toString());
        alarmInfoV3.setAlarmName(alarmName);
        alarmInfoV3.setAlarmNameCode(alarmNameCode);
        alarmInfoV3.setAlarmClassName(alarmClassName);
        alarmInfoV3.setAlarmClassCode(alarmClassCode);
        alarmInfoV3.setAlarmEquCode(equInfoVO.getEquCode());
        alarmInfoV3.setAlarmEquName(equInfoVO.getEquName());
        alarmInfoV3.setAlarmLevelCode(alarmLevelCode);
        alarmInfoV3.setAlarmLevelName(alarmLevelName);
        LocalDateTime now = LocalDateTime.now();
        String alarmTime = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        alarmInfoV3.setAlarmTime(alarmTime);
        alarmInfoV3.setAlarmStateCode(alarmStateCode);
        alarmInfoV3.setAlarmStateName(alarmStateName);
        alarmInfoV3.setSystemCode(systemCode);
        alarmInfoV3.setSystemName(systemName);
        alarmInfoV3.setAirportIata(airportIata);
        alarmInfoV3.setAirportName(airportName);
        return alarmInfoV3;
    }

    private EquInfoVO getEquInfo(String deviceIp) {
        List<EquInfoVO> equList = getEquList();
        Map<String, EquInfoVO> equInfoVOMap = equList.stream().collect(Collectors.toMap(EquInfoVO::getEquIp, item -> item));
        return equInfoVOMap.get(deviceIp);
    }


}
