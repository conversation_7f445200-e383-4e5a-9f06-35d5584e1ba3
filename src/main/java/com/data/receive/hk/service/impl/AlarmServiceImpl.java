package com.data.receive.hk.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.data.receive.hk.domain.vo.AlarmInfoV3;
import com.data.receive.hk.domain.vo.Meta;
import com.data.receive.hk.service.AlarmService;
import com.data.receive.hk.service.EquService;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2025/7/7
 */
@Service
public class AlarmServiceImpl implements AlarmService {
    private static final Logger log = LoggerFactory.getLogger(AlarmServiceImpl.class);
    @Resource
    private DefaultMQProducer producer;
    @Resource
    private EquService equService;
    @Value("${alarm.topic}")
    private String topic;
    @Value("${meta.sender}")
    private String sender;
    @Value("${meta.receiver}")
    private String receiver;
    @Value("${meta.msg.type}")
    private String msgType;
    @Value("${meta.event.type}")
    private String eventType;
    @Value("${alarm.filter.timeout}")
    private int alarmFilterTimeout;

    /**
     * 存储设备编码和报警超时时间的映射
     * key: 设备编码, value: 报警超时时间
     */
    private final Map<String, LocalDateTime> alarmTimeoutMap = new ConcurrentHashMap<>();

    @Override
    public void addAlarm(String deviceIp, int alarmType) {
        log.info("报警类型：{}",alarmType);
        /**
         * 报警类型：0-信号量报警，1-硬盘满，2-信号丢失，3-移动侦测，4-硬盘未格式化，5-读写硬盘出错，6-遮挡报警，7-制式不匹配，8-非法访问，9-视频信号异常，10-录像/抓图异常，11-智能场景变化，12-阵列异常，13-前端/录像分辨率不匹配，15-智能侦测，16-POE供电异常，17-闪光灯异常，18-磁盘满负荷异常报警，19-音频丢失，23-脉冲报警，24-人脸库硬盘异常，25-人脸库变更，26-人脸库图片变更
         */
        AlarmInfoV3 alarmInfoV3 = equService.getAlarmInfo(deviceIp);
        if (alarmInfoV3 == null) {
            log.error("获取报警信息失败，设备IP：{}", deviceIp);
            return;
        }

        // 报警过滤逻辑
        String equCode = alarmInfoV3.getAlarmEquCode();
        LocalDateTime currentTime = LocalDateTime.now();

        // 检查是否需要过滤报警
        if (shouldFilterAlarm(equCode, currentTime)) {
            log.info("报警被过滤，设备编码：{}，当前时间：{}", equCode, currentTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            return;
        }

        // 更新设备的报警超时时间
        LocalDateTime timeoutTime = currentTime.plusSeconds(alarmFilterTimeout);
        alarmTimeoutMap.put(equCode, timeoutTime);
        log.info("更新设备报警超时时间，设备编码：{}，超时时间：{}", equCode, timeoutTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        Meta meta = new Meta();
        meta.setSender(sender);
        meta.setReceiver(receiver);
        meta.setSequence(UUID.randomUUID().toString());
        meta.setRecvSequence("");
        LocalDateTime now = LocalDateTime.now();
        String formattedTime = now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        meta.setSendTime(formattedTime);
        meta.setRecvTime("");
        meta.setForwardTime(formattedTime);
        meta.setMsgType(msgType);
        meta.setEventType(eventType);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("meta",meta);
        jsonObject.put("body",alarmInfoV3);

        try {
            Message msg = new Message(topic, jsonObject.toJSONString().getBytes("utf-8"));
            SendResult sendResult = producer.send(msg);
            log.info("发送成功状态为》》》"+sendResult.getSendStatus()+",MsgId为》》》"+sendResult.getMsgId());
        }catch (Exception e){
            log.error("发送失败：",e);
        }
    }

    /**
     * 判断是否应该过滤报警
     * @param equCode 设备编码
     * @param currentTime 当前时间
     * @return true-需要过滤，false-不需要过滤
     */
    private boolean shouldFilterAlarm(String equCode, LocalDateTime currentTime) {
        LocalDateTime timeoutTime = alarmTimeoutMap.get(equCode);
        if (timeoutTime == null) {
            // 第一次报警，不过滤
            return false;
        }

        // 如果当前时间小于等于超时时间，则过滤报警
        return currentTime.isBefore(timeoutTime) || currentTime.isEqual(timeoutTime);
    }
}
