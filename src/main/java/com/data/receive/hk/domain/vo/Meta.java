package com.data.receive.hk.domain.vo;

/**
 * 消息头标签
 *
 * <AUTHOR>
 */
public class Meta {

    /**
     * 消息发送者
     */
    private String sender = new String();

    /**
     * 消息接收者
     */
    private String receiver = new String();

    /**
     * 消息序号
     */
    private String sequence = new String();

    /**
     * 接收到的消息序号
     */
    private String recvSequence = new String();

    /**
     * 发送时间
     */
    private String sendTime = new String();

    /**
     * sdk采集时间
     */
    private String recvTime = new String();

    /**
     * 转发时间
     */
    private String forwardTime = new String();

    /**
     * 消息类型
     */
    private String msgType = new String();

    /**
     * 事件类型
     */
    private String eventType = new String();

    /**
     * @param 消息发送者
     */
    public void setSender(String sender) {
        this.sender = sender;
    }

    /**
     * @return 消息发送者
     */
    public String getSender() {
        return sender;
    }

    /**
     * @param 消息接收者
     */
    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    /**
     * @return 消息接收者
     */
    public String getReceiver() {
        return receiver;
    }

    /**
     * @param 消息序号
     */
    public void setSequence(String sequence) {
        this.sequence = sequence;
    }

    /**
     * @return 消息序号
     */
    public String getSequence() {
        return sequence;
    }

    /**
     * @return 接收到的消息序号
     */
    public String getRecvSequence() {
        return recvSequence;
    }

    /**
     * @param 接收到的消息序号
     */
    public void setRecvSequence(String recvSequence) {
        this.recvSequence = recvSequence;
    }

    /**
     * @param 发送时间
     */
    public void setSendTime(String sendTime) {
        this.sendTime = sendTime;
    }

    /**
     * @return 发送时间
     */
    public String getSendTime() {
        return sendTime;
    }

    /**
     * @return sdk采集时间
     */
    public String getRecvTime() {
        return recvTime;
    }

    /**
     * @param sdk采集时间
     */
    public void setRecvTime(String recvTime) {
        this.recvTime = recvTime;
    }

    /**
     * @return 转发时间
     */
    public String getForwardTime() {
        return forwardTime;
    }

    /**
     * @param 转发时间
     */
    public void setForwardTime(String forwardTime) {
        this.forwardTime = forwardTime;
    }

    /**
     * @param 消息类型
     */
    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    /**
     * @return 消息类型
     */
    public String getMsgType() {
        return msgType;
    }

    /**
     * @return 事件类型
     */
    public String getEventType() {
        return eventType;
    }

    /**
     * @param 事件类型
     */
    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

}
