package com.data.receive.hk.domain.vo;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class AlarmInfoV3 {

    /**
     * 报警来源ID
     */
    private String alarmSourceId;

    /**
     * 报警分类名称
     */
    private String alarmClassName;

    /**
     * 报警分类编码
     */
    private String alarmClassCode;

    /**
     * 报警名称编码
     */
    private String alarmNameCode;

    /**
     * 报警名称
     */
    private String alarmName;

    /**
     * 报警级别代码
     */
    private String alarmLevelCode;

    /**
     * 报警级别名称
     */
    private String alarmLevelName;

    /**
     * 报警时间
     */
    private String alarmTime;

    /**
     * 报警状态代码
     */
    private String alarmStateCode;

    /**
     * 报警状态名称
     */
    private String alarmStateName;

    /**
     * 报警可视化图标
     */
    private String alarmPictureUrl;

    /**
     * 报警颜色
     */
    private String areaCode;

    /**
     * 报警所在图层代码
     */
    private String alarmEquCode;

    /**
     * 报警所在图层名称
     */
    private String alarmEquName;

    /**
     * 报警所在区域编码
     */
    private String cardReaderCode;

    /**
     * 报警所在区域名称
     */
    private String cardReaderName;

    /**
     * 报警设备名称
     */
    private String systemCode;

    /**
     * 报警设备分类代码
     */
    private String systemName;

    /**
     * 报警描述
     */
    private String alarmDescribe;

    /**
     * 机场三字码
     */
    private String airportIata;

    /**
     * 机场名称
     */
    private String airportName;

    public String getAlarmSourceId() {
        return alarmSourceId;
    }

    public void setAlarmSourceId(String alarmSourceId) {
        this.alarmSourceId = alarmSourceId;
    }

    public String getAlarmClassName() {
        return alarmClassName;
    }

    public void setAlarmClassName(String alarmClassName) {
        this.alarmClassName = alarmClassName;
    }

    public String getAlarmClassCode() {
        return alarmClassCode;
    }

    public void setAlarmClassCode(String alarmClassCode) {
        this.alarmClassCode = alarmClassCode;
    }

    public String getAlarmNameCode() {
        return alarmNameCode;
    }

    public void setAlarmNameCode(String alarmNameCode) {
        this.alarmNameCode = alarmNameCode;
    }

    public String getAlarmName() {
        return alarmName;
    }

    public void setAlarmName(String alarmName) {
        this.alarmName = alarmName;
    }

    public String getAlarmLevelCode() {
        return alarmLevelCode;
    }

    public void setAlarmLevelCode(String alarmLevelCode) {
        this.alarmLevelCode = alarmLevelCode;
    }

    public String getAlarmLevelName() {
        return alarmLevelName;
    }

    public void setAlarmLevelName(String alarmLevelName) {
        this.alarmLevelName = alarmLevelName;
    }

    public String getAlarmTime() {
        return alarmTime;
    }

    public void setAlarmTime(String alarmTime) {
        this.alarmTime = alarmTime;
    }

    public String getAlarmStateCode() {
        return alarmStateCode;
    }

    public void setAlarmStateCode(String alarmStateCode) {
        this.alarmStateCode = alarmStateCode;
    }

    public String getAlarmStateName() {
        return alarmStateName;
    }

    public void setAlarmStateName(String alarmStateName) {
        this.alarmStateName = alarmStateName;
    }

    public String getAlarmPictureUrl() {
        return alarmPictureUrl;
    }

    public void setAlarmPictureUrl(String alarmPictureUrl) {
        this.alarmPictureUrl = alarmPictureUrl;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAlarmEquCode() {
        return alarmEquCode;
    }

    public void setAlarmEquCode(String alarmEquCode) {
        this.alarmEquCode = alarmEquCode;
    }

    public String getAlarmEquName() {
        return alarmEquName;
    }

    public void setAlarmEquName(String alarmEquName) {
        this.alarmEquName = alarmEquName;
    }

    public String getCardReaderCode() {
        return cardReaderCode;
    }

    public void setCardReaderCode(String cardReaderCode) {
        this.cardReaderCode = cardReaderCode;
    }

    public String getCardReaderName() {
        return cardReaderName;
    }

    public void setCardReaderName(String cardReaderName) {
        this.cardReaderName = cardReaderName;
    }

    public String getSystemCode() {
        return systemCode;
    }

    public void setSystemCode(String systemCode) {
        this.systemCode = systemCode;
    }

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public String getAlarmDescribe() {
        return alarmDescribe;
    }

    public void setAlarmDescribe(String alarmDescribe) {
        this.alarmDescribe = alarmDescribe;
    }

    public String getAirportIata() {
        return airportIata;
    }

    public void setAirportIata(String airportIata) {
        this.airportIata = airportIata;
    }

    public String getAirportName() {
        return airportName;
    }

    public void setAirportName(String airportName) {
        this.airportName = airportName;
    }
}